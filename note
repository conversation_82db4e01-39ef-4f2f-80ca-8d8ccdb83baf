sudo apt-get update
sudo apt upgrade -y
sudo apt install python3-pip python3-dev
sudo apt install python3-virtualenv
sudo apt-get install ffmpeg
# sudo pip3 install virtualenv

mkdir ~/vido_tide

cd ~/vido_tide
# virtualenv .venv
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt

cd /home/<USER>/PycharmProjects/vido_ht/vido_tide/

pip install -e /home/<USER>/PycharmProjects/vido_ht/vido_tide
pip uninstall -e /home/<USER>/PycharmProjects/vido_ht/vido_tide
pip uninstall engagement_app
pip uninstall audience-engagement-detection

source .venv/bin/activate && python engagement_app/tests/test_sample_data_integration.py

source .venv/bin/activate && python engagement_app/scripts/run_pipeline.py   \
  --episode_id "podcast_ep_123" \
  --transcript_path "sample/audio1/transcript/transcript.json" \
  --output_dir "outputs/"
