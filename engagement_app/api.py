"""
High-Level Integration API for Audience Engagement Detection

This module provides simplified APIs for integrating the engagement detection
pipeline into other projects with minimal configuration.
"""

import logging
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
import yaml

from .pipeline import (
    DataLoader, TranscriptPreprocessor, FeatureExtractor,
    EngagementScorer, HighlightGenerator, LLMReranker, JSONExporter
)
from .pipeline.load_data import WhisperTranscript, AudioData
from .pipeline.postprocess import HighlightSegment

logger = logging.getLogger(__name__)


class EngagementDetector:
    """
    High-level API for audience engagement detection.
    
    Provides a simple interface for processing audio/video content
    and extracting engagement highlights.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the engagement detector.
        
        Args:
            config: Optional configuration dictionary. If None, uses default config.
        """
        self.config = config or self._get_default_config()
        self._initialize_components()
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for the pipeline."""
        config_path = Path(__file__).parent / "config" / "pipeline_config.yaml"
        
        if config_path.exists():
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        else:
            # Fallback minimal config
            return {
                'features': {
                    'text': {'emotion_threshold': 0.1, 'sentiment_window': 3},
                    'audio': {'sample_rate': 16000, 'laughter_threshold': 0.5}
                },
                'scoring': {
                    'weights': {
                        'emotion': 0.30, 'sentiment_delta': 0.15, 'rhetorical': 0.10,
                        'topic_shift': 0.20, 'keyword_salience': 0.05, 'laughter': 0.07,
                        'applause': 0.05, 'prosody': 0.05, 'clap_similarity': 0.03
                    },
                    'normalization': {'method': 'minmax'}
                },
                'highlights': {
                    'selection_threshold_std': 1.0, 'min_duration_sec': 30,
                    'max_duration_sec': 180, 'top_k': 10
                },
                'output': {'version': 'engage-v1.0', 'include_debug_info': True}
            }
    
    def _initialize_components(self):
        """Initialize pipeline components."""
        self.data_loader = DataLoader(target_sample_rate=16000)
        self.preprocessor = TranscriptPreprocessor()
        self.feature_extractor = FeatureExtractor(self.config.get('features', {}))
        self.scorer = EngagementScorer(self.config.get('scoring', {}))
        self.highlight_generator = HighlightGenerator(self.config.get('highlights', {}))
        self.json_exporter = JSONExporter(self.config.get('output', {}))
        
        # Optional LLM reranker
        llm_config = self.config.get('llm', {})
        if llm_config.get('enabled', False):
            self.llm_reranker = LLMReranker(llm_config)
        else:
            self.llm_reranker = None
    
    def process_files(self, 
                     transcript_path: Union[str, Path],
                     audio_path: Optional[Union[str, Path]] = None,
                     episode_id: Optional[str] = None) -> List[HighlightSegment]:
        """
        Process transcript and audio files to extract engagement highlights.
        
        Args:
            transcript_path: Path to Whisper transcript JSON file
            audio_path: Optional path to audio file
            episode_id: Optional episode identifier
            
        Returns:
            List of HighlightSegment objects
        """
        if episode_id is None:
            episode_id = Path(transcript_path).stem
            
        logger.info(f"Processing episode: {episode_id}")
        
        # Load data
        transcript = self.data_loader.load_transcript(transcript_path)
        audio_data = None
        if audio_path:
            audio_data = self.data_loader.load_audio(audio_path)
            self.data_loader.validate_alignment(transcript, audio_data)
        
        # Process through pipeline
        return self._process_data(transcript, audio_data, episode_id)
    
    def process_data(self,
                    transcript: WhisperTranscript,
                    audio_data: Optional[AudioData] = None,
                    episode_id: str = "episode") -> List[HighlightSegment]:
        """
        Process transcript and audio data objects directly.
        
        Args:
            transcript: WhisperTranscript object
            audio_data: Optional AudioData object
            episode_id: Episode identifier
            
        Returns:
            List of HighlightSegment objects
        """
        return self._process_data(transcript, audio_data, episode_id)
    
    def _process_data(self,
                     transcript: WhisperTranscript,
                     audio_data: Optional[AudioData],
                     episode_id: str) -> List[HighlightSegment]:
        """Internal method to process data through the pipeline."""
        # Preprocess transcript
        df = self.preprocessor.process_transcript(transcript, episode_id)
        
        # Extract features
        df = self.feature_extractor.extract_features(df, audio_data)
        
        # Calculate engagement scores
        df = self.scorer.calculate_engagement_scores(df)
        
        # Generate highlights
        highlights = self.highlight_generator.generate_highlights(df)
        
        # Optional LLM reranking
        if self.llm_reranker:
            highlights = self.llm_reranker.rerank_segments(highlights)
        
        return highlights
    
    def export_highlights(self,
                         highlights: List[HighlightSegment],
                         episode_id: str,
                         output_path: Union[str, Path],
                         processing_stats: Optional[Dict[str, Any]] = None) -> bool:
        """
        Export highlights to JSON file.
        
        Args:
            highlights: List of HighlightSegment objects
            episode_id: Episode identifier
            output_path: Output file path
            processing_stats: Optional processing statistics
            
        Returns:
            True if successful, False otherwise
        """
        if processing_stats is None:
            processing_stats = {
                'total_duration_sec': 0,
                'segments_analyzed': len(highlights),
                'processing_time_sec': 0
            }
        
        return self.json_exporter.export_highlights(
            episode_id, highlights, processing_stats, str(output_path)
        )


class QuickProcessor:
    """
    Ultra-simplified API for quick processing with minimal configuration.
    """
    
    @staticmethod
    def extract_highlights(transcript_path: Union[str, Path],
                          audio_path: Optional[Union[str, Path]] = None,
                          top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Quick highlight extraction with minimal configuration.
        
        Args:
            transcript_path: Path to Whisper transcript JSON
            audio_path: Optional path to audio file
            top_k: Number of top highlights to return
            
        Returns:
            List of highlight dictionaries with simplified structure
        """
        # Use minimal config for speed
        config = {
            'highlights': {'top_k': top_k, 'min_duration_sec': 20},
            'output': {'include_debug_info': False}
        }
        
        detector = EngagementDetector(config)
        highlights = detector.process_files(transcript_path, audio_path)
        
        # Convert to simplified format
        return [
            {
                'start_sec': h.start_sec,
                'end_sec': h.end_sec,
                'duration': h.duration,
                'transcript': h.transcript,
                'engagement_score': h.max_engagement_score,
                'cues': h.cues
            }
            for h in highlights
        ]
    
    @staticmethod
    def process_to_json(transcript_path: Union[str, Path],
                       output_path: Union[str, Path],
                       audio_path: Optional[Union[str, Path]] = None,
                       top_k: int = 10) -> bool:
        """
        Process files and export directly to JSON.
        
        Args:
            transcript_path: Path to Whisper transcript JSON
            output_path: Output JSON file path
            audio_path: Optional path to audio file
            top_k: Number of top highlights to return
            
        Returns:
            True if successful, False otherwise
        """
        detector = EngagementDetector()
        episode_id = Path(transcript_path).stem
        
        highlights = detector.process_files(transcript_path, audio_path, episode_id)
        
        # Limit to top_k
        if len(highlights) > top_k:
            highlights = highlights[:top_k]
        
        return detector.export_highlights(highlights, episode_id, output_path)


# Convenience functions for backward compatibility
def detect_engagement(transcript_path: Union[str, Path],
                     audio_path: Optional[Union[str, Path]] = None,
                     config: Optional[Dict[str, Any]] = None) -> List[HighlightSegment]:
    """
    Convenience function for engagement detection.
    
    Args:
        transcript_path: Path to Whisper transcript JSON
        audio_path: Optional path to audio file
        config: Optional configuration dictionary
        
    Returns:
        List of HighlightSegment objects
    """
    detector = EngagementDetector(config)
    return detector.process_files(transcript_path, audio_path)


def quick_highlights(transcript_path: Union[str, Path],
                    audio_path: Optional[Union[str, Path]] = None,
                    top_k: int = 5) -> List[Dict[str, Any]]:
    """
    Quick highlight extraction with minimal setup.
    
    Args:
        transcript_path: Path to Whisper transcript JSON
        audio_path: Optional path to audio file
        top_k: Number of highlights to return
        
    Returns:
        List of simplified highlight dictionaries
    """
    return QuickProcessor.extract_highlights(transcript_path, audio_path, top_k)
