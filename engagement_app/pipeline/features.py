"""
Feature Extraction Module

Extracts text and audio features for engagement detection including:
- Emotion scores using transformer models
- Sentiment volatility analysis
- Rhetorical engagement patterns
- Topic shift detection
- Audio features (laughter, applause, prosody)
"""

import logging
import re
import warnings
from typing import Dict, List, Optional, Tuple, Any

import librosa
import numpy as np
import pandas as pd
import torch
from bertopic import BERTopic
from keybert import KeyBERT
from nltk.sentiment import SentimentIntensityAnalyzer
from panns_inference import AudioTagging, SoundEventDetection
from scipy.spatial.distance import jensenshannon
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import MinMaxScaler
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification

from .load_data import AudioData

# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

logger = logging.getLogger(__name__)


class TextFeatureExtractor:
    """Extracts engagement features from transcript text."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize text feature extractor.
        
        Args:
            config: Configuration dictionary with model settings
        """
        self.config = config
        self.emotion_model_name = config.get('emotion_model', 'joeddav/distilbert-go-emotions')
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # Initialize models lazily
        self._emotion_classifier = None
        self._sentiment_analyzer = None
        self._topic_model = None
        self._keyword_extractor = None
        self._tfidf_vectorizer = None
        
        # Rhetorical patterns
        self.rhetorical_patterns = [
            re.compile(pattern, re.IGNORECASE) 
            for pattern in config.get('rhetorical_patterns', [])
        ]
        
    @property
    def emotion_classifier(self):
        """Lazy load emotion classification model."""
        if self._emotion_classifier is None:
            logger.info(f"Loading emotion model: {self.emotion_model_name}")
            self._emotion_classifier = pipeline(
                "text-classification",
                model=self.emotion_model_name,
                device=0 if self.device == 'cuda' else -1,
                return_all_scores=True
            )
        return self._emotion_classifier
    
    @property
    def sentiment_analyzer(self):
        """Lazy load VADER sentiment analyzer."""
        if self._sentiment_analyzer is None:
            try:
                import nltk
                nltk.download('vader_lexicon', quiet=True)
                self._sentiment_analyzer = SentimentIntensityAnalyzer()
                logger.info("Loaded VADER sentiment analyzer")
            except Exception as e:
                logger.error(f"Failed to load VADER: {e}")
                raise
        return self._sentiment_analyzer
    
    @property
    def topic_model(self):
        """Lazy load BERTopic model."""
        if self._topic_model is None:
            topic_config = self.config.get('topic_model', {})
            self._topic_model = BERTopic(
                nr_topics=topic_config.get('n_topics', 10),
                min_topic_size=topic_config.get('min_topic_size', 5),
                verbose=False
            )
            logger.info("Initialized BERTopic model")
        return self._topic_model
    
    @property
    def keyword_extractor(self):
        """Lazy load KeyBERT extractor."""
        if self._keyword_extractor is None:
            self._keyword_extractor = KeyBERT()
            logger.info("Loaded KeyBERT extractor")
        return self._keyword_extractor
    
    def extract_emotion_scores(self, texts: List[str]) -> np.ndarray:
        """
        Extract emotion scores focusing on engagement-related emotions.
        
        Args:
            texts: List of text strings
            
        Returns:
            Array of emotion scores (joy + anger + surprise + excitement)
        """
        if not texts:
            return np.array([])
            
        logger.info(f"Extracting emotion scores for {len(texts)} texts")
        
        try:
            # Process in batches to avoid memory issues
            batch_size = self.config.get('batch_size', 16)
            all_scores = []
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                batch_results = self.emotion_classifier(batch_texts)
                
                batch_scores = []
                for result in batch_results:
                    # Sum probabilities for engagement-related emotions
                    # Updated for SamLowe/roberta-base-go_emotions model labels
                    engagement_emotions = [
                        'joy', 'anger', 'surprise', 'excitement', 'amusement',
                        'admiration', 'curiosity', 'desire', 'optimism'
                    ]
                    score = sum(
                        item['score'] for item in result
                        if item['label'].lower() in engagement_emotions
                    )
                    batch_scores.append(score)
                
                all_scores.extend(batch_scores)
            
            return np.array(all_scores)
            
        except Exception as e:
            logger.error(f"Error extracting emotion scores: {e}")
            return np.zeros(len(texts))
    
    def extract_sentiment_volatility(self, texts: List[str], window_size: int = 3) -> np.ndarray:
        """
        Calculate sentiment volatility as differences in compound scores.
        
        Args:
            texts: List of text strings
            window_size: Window size for volatility calculation
            
        Returns:
            Array of sentiment volatility scores
        """
        if len(texts) < 2:
            return np.zeros(len(texts))
            
        logger.info(f"Calculating sentiment volatility for {len(texts)} texts")
        
        try:
            # Get compound sentiment scores
            compound_scores = []
            for text in texts:
                scores = self.sentiment_analyzer.polarity_scores(text)
                compound_scores.append(scores['compound'])
            
            compound_scores = np.array(compound_scores)
            
            # Calculate volatility as absolute differences
            volatility = np.zeros(len(texts))
            for i in range(1, len(texts)):
                start_idx = max(0, i - window_size)
                window_scores = compound_scores[start_idx:i+1]
                if len(window_scores) > 1:
                    volatility[i] = np.std(window_scores)
            
            return volatility
            
        except Exception as e:
            logger.error(f"Error calculating sentiment volatility: {e}")
            return np.zeros(len(texts))
    
    def extract_rhetorical_features(self, texts: List[str]) -> np.ndarray:
        """
        Extract rhetorical engagement features.
        
        Args:
            texts: List of text strings
            
        Returns:
            Array of rhetorical engagement scores
        """
        logger.info(f"Extracting rhetorical features for {len(texts)} texts")
        
        scores = []
        for text in texts:
            score = 0
            text_len = len(text.split())
            
            if text_len > 0:
                # Count pattern matches
                for pattern in self.rhetorical_patterns:
                    matches = len(pattern.findall(text))
                    score += matches / text_len  # Normalize by text length
            
            scores.append(score)
        
        return np.array(scores)
    
    def extract_topic_shifts(self, texts: List[str], window_size: int = 5) -> np.ndarray:
        """
        Detect topic shifts using BERTopic with sliding window.
        
        Args:
            texts: List of text strings
            window_size: Size of sliding window
            
        Returns:
            Array of topic shift scores
        """
        if len(texts) < window_size:
            return np.zeros(len(texts))
            
        logger.info(f"Detecting topic shifts for {len(texts)} texts")
        
        try:
            # Fit topic model on all texts
            topics, _ = self.topic_model.fit_transform(texts)
            topic_probs = self.topic_model.probabilities_
            
            if topic_probs is None:
                logger.warning("Topic probabilities not available, using topic assignments")
                # Convert topic assignments to one-hot probabilities
                n_topics = len(set(topics))
                topic_probs = np.zeros((len(topics), n_topics))
                for i, topic in enumerate(topics):
                    if topic >= 0:  # Ignore outlier topics (-1)
                        topic_probs[i, topic] = 1.0
            
            # Calculate topic shifts using Jensen-Shannon divergence
            shift_scores = np.zeros(len(texts))
            
            for i in range(window_size, len(texts)):
                # Compare current window with previous window
                current_window = topic_probs[i-window_size+1:i+1]
                previous_window = topic_probs[i-window_size:i]
                
                # Average topic distributions
                current_dist = np.mean(current_window, axis=0)
                previous_dist = np.mean(previous_window, axis=0)
                
                # Calculate Jensen-Shannon divergence
                js_div = jensenshannon(current_dist, previous_dist)
                shift_scores[i] = js_div if not np.isnan(js_div) else 0
            
            return shift_scores
            
        except Exception as e:
            logger.error(f"Error detecting topic shifts: {e}")
            return np.zeros(len(texts))
    
    def extract_keyword_salience(self, texts: List[str], top_k: int = 10) -> np.ndarray:
        """
        Extract keyword salience scores combining TF-IDF and KeyBERT.
        
        Args:
            texts: List of text strings
            top_k: Number of top keywords to consider
            
        Returns:
            Array of keyword salience scores
        """
        if not texts:
            return np.array([])
            
        logger.info(f"Extracting keyword salience for {len(texts)} texts")
        
        try:
            # Initialize TF-IDF vectorizer
            if self._tfidf_vectorizer is None:
                self._tfidf_vectorizer = TfidfVectorizer(
                    max_features=1000,
                    stop_words='english',
                    ngram_range=(1, 2)
                )
                tfidf_matrix = self._tfidf_vectorizer.fit_transform(texts)
            else:
                tfidf_matrix = self._tfidf_vectorizer.transform(texts)
            
            # Get TF-IDF scores
            tfidf_scores = np.array(tfidf_matrix.sum(axis=1)).flatten()
            
            # Get KeyBERT scores for each text
            keybert_scores = []
            for text in texts:
                try:
                    keywords = self.keyword_extractor.extract_keywords(
                        text, keyphrase_ngram_range=(1, 2), stop_words='english', top_k=top_k
                    )
                    # Sum keyword scores
                    score = sum(score for _, score in keywords) if keywords else 0
                    keybert_scores.append(score)
                except:
                    keybert_scores.append(0)
            
            keybert_scores = np.array(keybert_scores)
            
            # Combine scores (normalize first)
            scaler = MinMaxScaler()
            if len(tfidf_scores) > 1:
                tfidf_norm = scaler.fit_transform(tfidf_scores.reshape(-1, 1)).flatten()
            else:
                tfidf_norm = tfidf_scores
                
            if len(keybert_scores) > 1:
                keybert_norm = scaler.fit_transform(keybert_scores.reshape(-1, 1)).flatten()
            else:
                keybert_norm = keybert_scores
            
            # Weighted combination
            combined_scores = 0.6 * tfidf_norm + 0.4 * keybert_norm
            
            return combined_scores
            
        except Exception as e:
            logger.error(f"Error extracting keyword salience: {e}")
            return np.zeros(len(texts))
    
    def extract_all_text_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Extract all text features and add to DataFrame.
        
        Args:
            df: DataFrame with text column
            
        Returns:
            DataFrame with added text feature columns
        """
        texts = df['text'].tolist()
        
        logger.info("Extracting all text features")
        
        # Extract features
        df['emotion_score'] = self.extract_emotion_scores(texts)
        df['sentiment_volatility'] = self.extract_sentiment_volatility(texts)
        df['rhetorical_score'] = self.extract_rhetorical_features(texts)
        df['topic_shift_score'] = self.extract_topic_shifts(texts)
        df['keyword_salience'] = self.extract_keyword_salience(texts)
        
        logger.info("Text feature extraction completed")

        return df


class AudioFeatureExtractor:
    """Extracts engagement features from audio data."""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize audio feature extractor.

        Args:
            config: Configuration dictionary with audio settings
        """
        self.config = config
        self.sample_rate = config.get('sample_rate', 16000)
        self.hop_length = config.get('hop_length', 512)
        self.frame_length = config.get('frame_length', 2048)

        # Initialize models lazily
        self._audio_tagger = None
        self._sound_detector = None

    @property
    def audio_tagger(self):
        """Lazy load PANNs audio tagger."""
        if self._audio_tagger is None:
            try:
                self._audio_tagger = AudioTagging(checkpoint_path=None, device='cuda' if torch.cuda.is_available() else 'cpu')
                logger.info("Loaded PANNs audio tagger")
            except Exception as e:
                logger.error(f"Failed to load PANNs audio tagger: {e}")
                self._audio_tagger = None
        return self._audio_tagger

    @property
    def sound_detector(self):
        """Lazy load PANNs sound event detector."""
        if self._sound_detector is None:
            try:
                self._sound_detector = SoundEventDetection(checkpoint_path=None, device='cuda' if torch.cuda.is_available() else 'cpu')
                logger.info("Loaded PANNs sound event detector")
            except Exception as e:
                logger.error(f"Failed to load PANNs sound detector: {e}")
                self._sound_detector = None
        return self._sound_detector

    def extract_laughter_applause(self, audio_data: AudioData, timestamps: List[Tuple[float, float]]) -> Tuple[np.ndarray, np.ndarray]:
        """
        Detect laughter and applause in audio segments.

        Args:
            audio_data: Audio data object
            timestamps: List of (start, end) timestamps for segments

        Returns:
            Tuple of (laughter_scores, applause_scores)
        """
        if self.audio_tagger is None:
            logger.warning("Audio tagger not available, returning zero scores")
            return np.zeros(len(timestamps)), np.zeros(len(timestamps))

        logger.info(f"Detecting laughter/applause for {len(timestamps)} segments")

        laughter_scores = []
        applause_scores = []

        for start_sec, end_sec in timestamps:
            try:
                # Extract audio segment
                start_sample = int(start_sec * self.sample_rate)
                end_sample = int(end_sec * self.sample_rate)
                segment_audio = audio_data.audio[start_sample:end_sample]

                if len(segment_audio) < 1024:  # Too short for analysis
                    laughter_scores.append(0.0)
                    applause_scores.append(0.0)
                    continue

                # Get audio tags
                clipwise_output, _ = self.audio_tagger.inference(segment_audio[None, :])

                # Extract specific event probabilities
                # Note: These indices are for AudioSet classes - may need adjustment
                laughter_idx = 321  # Laughter class in AudioSet
                applause_idx = 138  # Applause class in AudioSet

                laughter_prob = clipwise_output[0, laughter_idx] if clipwise_output.shape[1] > laughter_idx else 0.0
                applause_prob = clipwise_output[0, applause_idx] if clipwise_output.shape[1] > applause_idx else 0.0

                laughter_scores.append(float(laughter_prob))
                applause_scores.append(float(applause_prob))

            except Exception as e:
                logger.warning(f"Error processing audio segment {start_sec}-{end_sec}: {e}")
                laughter_scores.append(0.0)
                applause_scores.append(0.0)

        return np.array(laughter_scores), np.array(applause_scores)

    def extract_prosodic_emphasis(self, audio_data: AudioData, timestamps: List[Tuple[float, float]]) -> np.ndarray:
        """
        Extract prosodic emphasis features using RMS energy.

        Args:
            audio_data: Audio data object
            timestamps: List of (start, end) timestamps for segments

        Returns:
            Array of prosodic emphasis scores
        """
        logger.info(f"Extracting prosodic features for {len(timestamps)} segments")

        # Calculate RMS energy for entire audio
        rms_energy = librosa.feature.rms(
            y=audio_data.audio,
            frame_length=self.frame_length,
            hop_length=self.hop_length
        )[0]

        # Calculate global statistics
        mean_energy = np.mean(rms_energy)
        std_energy = np.std(rms_energy)
        threshold = mean_energy + self.config.get('prosody_std_multiplier', 1.0) * std_energy

        prosody_scores = []

        for start_sec, end_sec in timestamps:
            try:
                # Convert time to frame indices
                start_frame = int(start_sec * self.sample_rate / self.hop_length)
                end_frame = int(end_sec * self.sample_rate / self.hop_length)

                # Extract RMS for segment
                segment_rms = rms_energy[start_frame:end_frame]

                if len(segment_rms) == 0:
                    prosody_scores.append(0.0)
                    continue

                # Calculate emphasis score
                max_energy = np.max(segment_rms)
                emphasis_score = 1.0 if max_energy > threshold else max_energy / threshold

                prosody_scores.append(emphasis_score)

            except Exception as e:
                logger.warning(f"Error processing prosody for segment {start_sec}-{end_sec}: {e}")
                prosody_scores.append(0.0)

        return np.array(prosody_scores)

    def extract_all_audio_features(self, audio_data: AudioData, df: pd.DataFrame) -> pd.DataFrame:
        """
        Extract all audio features and add to DataFrame.

        Args:
            audio_data: Audio data object
            df: DataFrame with timestamp columns

        Returns:
            DataFrame with added audio feature columns
        """
        timestamps = list(zip(df['start_sec'], df['end_sec']))

        logger.info("Extracting all audio features")

        # Extract features
        laughter_scores, applause_scores = self.extract_laughter_applause(audio_data, timestamps)
        prosody_scores = self.extract_prosodic_emphasis(audio_data, timestamps)

        # Add to DataFrame
        df['laughter_score'] = laughter_scores
        df['applause_score'] = applause_scores
        df['prosody_score'] = prosody_scores

        # Placeholder for CLAP similarity (optional advanced feature)
        df['clap_similarity'] = 0.0  # Would require CLAP model implementation

        logger.info("Audio feature extraction completed")

        return df


class FeatureExtractor:
    """Main feature extraction coordinator."""

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize feature extractor with configuration.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.text_extractor = TextFeatureExtractor(config.get('text', {}))
        self.audio_extractor = AudioFeatureExtractor(config.get('audio', {}))

    def extract_features(self, df: pd.DataFrame, audio_data: Optional[AudioData] = None) -> pd.DataFrame:
        """
        Extract all features from text and audio data.

        Args:
            df: DataFrame with preprocessed text data
            audio_data: Optional audio data for audio features

        Returns:
            DataFrame with all extracted features
        """
        logger.info("Starting feature extraction")

        # Extract text features
        df = self.text_extractor.extract_all_text_features(df)

        # Extract audio features if audio data is provided
        if audio_data is not None:
            df = self.audio_extractor.extract_all_audio_features(audio_data, df)
        else:
            logger.warning("No audio data provided, skipping audio features")
            # Add zero audio features
            df['laughter_score'] = 0.0
            df['applause_score'] = 0.0
            df['prosody_score'] = 0.0
            df['clap_similarity'] = 0.0

        logger.info("Feature extraction completed")

        return df
