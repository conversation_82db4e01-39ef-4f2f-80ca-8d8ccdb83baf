#!/usr/bin/env python3
"""
Setup script to install all required dependencies for Vido Tide.
This script ensures all models and data are properly downloaded and configured.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"   Error: {e.stderr.strip()}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    print("🔍 Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} is not supported. Please use Python 3.8+")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def install_requirements():
    """Install Python requirements."""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python requirements"
    )


def install_spacy_model():
    """Install spaCy English model."""
    return run_command(
        f"{sys.executable} -m spacy download en_core_web_sm",
        "Installing spaCy English model"
    )


def download_nltk_data():
    """Download required NLTK data."""
    return run_command(
        f"{sys.executable} -c \"import nltk; nltk.download('vader_lexicon', quiet=True); nltk.download('punkt', quiet=True)\"",
        "Downloading NLTK data"
    )


def verify_installation():
    """Verify that all components are properly installed."""
    print("\n🔍 Verifying installation...")
    
    # Test imports
    test_imports = [
        ("spacy", "import spacy; nlp = spacy.load('en_core_web_sm'); print('spaCy model loaded')"),
        ("transformers", "import transformers; print(f'Transformers version: {transformers.__version__}')"),
        ("torch", "import torch; print(f'PyTorch version: {torch.__version__}')"),
        ("librosa", "import librosa; print(f'Librosa version: {librosa.__version__}')"),
        ("nltk", "import nltk; from nltk.sentiment import SentimentIntensityAnalyzer; SentimentIntensityAnalyzer(); print('NLTK VADER loaded')"),
        ("pandas", "import pandas as pd; print(f'Pandas version: {pd.__version__}')"),
        ("numpy", "import numpy as np; print(f'NumPy version: {np.__version__}')"),
    ]
    
    all_passed = True
    for name, test_code in test_imports:
        try:
            result = subprocess.run(
                [sys.executable, "-c", test_code],
                capture_output=True, text=True, timeout=30
            )
            if result.returncode == 0:
                print(f"✅ {name}: {result.stdout.strip()}")
            else:
                print(f"❌ {name}: {result.stderr.strip()}")
                all_passed = False
        except subprocess.TimeoutExpired:
            print(f"❌ {name}: Import test timed out")
            all_passed = False
        except Exception as e:
            print(f"❌ {name}: {str(e)}")
            all_passed = False
    
    return all_passed


def create_cache_directories():
    """Create necessary cache directories."""
    print("\n🔄 Creating cache directories...")
    cache_dirs = [
        ".cache",
        "logs",
        "output"
    ]
    
    for cache_dir in cache_dirs:
        Path(cache_dir).mkdir(exist_ok=True)
        print(f"✅ Created directory: {cache_dir}")


def main():
    """Main setup function."""
    print("🚀 Vido Tide Dependency Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements. Please check your environment.")
        sys.exit(1)
    
    # Install spaCy model
    if not install_spacy_model():
        print("⚠️  spaCy model installation failed, but continuing...")
    
    # Download NLTK data
    if not download_nltk_data():
        print("⚠️  NLTK data download failed, but continuing...")
    
    # Create cache directories
    create_cache_directories()
    
    # Verify installation
    if verify_installation():
        print("\n🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Run the example: python examples/basic_integration.py")
        print("2. Check the documentation for more usage examples")
        print("3. Customize the configuration in engagement_app/config/pipeline_config.yaml")
    else:
        print("\n⚠️  Setup completed with some issues. Check the errors above.")
        print("You may need to install some dependencies manually.")
    
    print("\n" + "=" * 50)


if __name__ == "__main__":
    main()
