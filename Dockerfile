# Multi-stage Docker build for Vido Tide Engagement Detection
FROM python:3.10-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app
WORKDIR /home/<USER>

# Copy requirements and install Python dependencies
COPY --chown=app:app requirements.txt pyproject.toml ./
RUN pip install --user -r requirements.txt

# Production stage
FROM python:3.10-slim as production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
USER app
WORKDIR /home/<USER>

# Copy Python packages from base stage
COPY --from=base --chown=app:app /home/<USER>/.local /home/<USER>/.local

# Copy application code
COPY --chown=app:app engagement_app/ ./engagement_app/
COPY --chown=app:app pyproject.toml ./

# Install the package
RUN pip install --user -e .

# Download required models
RUN python -c "import spacy; spacy.cli.download('en_core_web_sm')"

# Set PATH to include user packages
ENV PATH="/home/<USER>/.local/bin:$PATH"

# Create directories for data
RUN mkdir -p /home/<USER>/data/input /home/<USER>/data/output

# Default command
CMD ["engagement-pipeline", "--help"]

# Development stage
FROM base as development

# Install development dependencies
RUN pip install --user -e ".[dev,test]"

# Copy all files for development
COPY --chown=app:app . .

# Download models
RUN python -c "import spacy; spacy.cli.download('en_core_web_sm')"

# Set PATH
ENV PATH="/home/<USER>/.local/bin:$PATH"

CMD ["bash"]
