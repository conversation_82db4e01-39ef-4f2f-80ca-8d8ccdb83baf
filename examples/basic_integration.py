#!/usr/bin/env python3
"""
Basic Integration Examples for Vido Tide

This script demonstrates various ways to integrate the engagement detection
system into your projects.
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from engagement_app.api import EngagementDetector, QuickProcessor, quick_highlights


def example_1_quick_start():
    """Example 1: Quick highlight extraction with minimal setup."""
    print("=== Example 1: Quick Start ===")
    
    # This is the simplest way to get highlights
    highlights = quick_highlights(
        transcript_path="sample/audio1/transcript/transcript.json",
        audio_path=None,  # Skip audio due to duration mismatch
        top_k=3
    )
    
    print(f"Found {len(highlights)} highlights:")
    for i, highlight in enumerate(highlights, 1):
        print(f"\n{i}. Highlight ({highlight['start_sec']:.1f}s - {highlight['end_sec']:.1f}s)")
        print(f"   Score: {highlight['engagement_score']:.3f}")
        print(f"   Cues: {', '.join(highlight['cues'])}")
        print(f"   Text: {highlight['transcript'][:100]}...")


def example_2_custom_config():
    """Example 2: Using custom configuration for specific use cases."""
    print("\n=== Example 2: Custom Configuration ===")
    
    # Custom config emphasizing emotional content and laughter
    config = {
        'features': {
            'text': {
                'emotion_threshold': 0.15,  # Lower threshold for more emotions
                'sentiment_window': 5
            },
            'audio': {
                'laughter_threshold': 0.3,  # Lower threshold for laughter
                'sample_rate': 16000
            }
        },
        'scoring': {
            'weights': {
                'emotion': 0.40,      # Emphasize emotions
                'laughter': 0.20,     # Emphasize laughter
                'sentiment_delta': 0.10,
                'rhetorical': 0.10,
                'topic_shift': 0.10,
                'keyword_salience': 0.05,
                'applause': 0.03,
                'prosody': 0.02
            }
        },
        'highlights': {
            'min_duration_sec': 20,   # Shorter highlights
            'max_duration_sec': 90,
            'top_k': 5
        }
    }
    
    detector = EngagementDetector(config)
    highlights = detector.process_files(
        transcript_path="sample/audio1/transcript/transcript.json",
        audio_path=None,  # Skip audio due to duration mismatch
        episode_id="custom_example"
    )
    
    print(f"Found {len(highlights)} highlights with custom config:")
    for highlight in highlights[:3]:
        print(f"- {highlight.start_sec:.1f}s-{highlight.end_sec:.1f}s: "
              f"Score {highlight.max_engagement_score:.3f}")


def example_3_batch_processing():
    """Example 3: Batch processing multiple files."""
    print("\n=== Example 3: Batch Processing ===")
    
    def process_directory(input_dir: str, output_dir: str):
        """Process all transcript files in a directory."""
        detector = EngagementDetector()
        
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        processed_count = 0
        
        for transcript_file in input_path.glob("*.json"):
            # Look for corresponding audio file
            audio_file = None
            for ext in ['.wav', '.mp3', '.flac']:
                potential_audio = input_path / f"{transcript_file.stem}{ext}"
                if potential_audio.exists():
                    audio_file = potential_audio
                    break
            
            try:
                # Process
                highlights = detector.process_files(
                    transcript_path=transcript_file,
                    audio_path=audio_file,
                    episode_id=transcript_file.stem
                )
                
                # Export
                output_file = output_path / f"{transcript_file.stem}_highlights.json"
                success = detector.export_highlights(
                    highlights=highlights,
                    episode_id=transcript_file.stem,
                    output_path=output_file
                )
                
                if success:
                    print(f"✓ Processed {transcript_file.name} -> {len(highlights)} highlights")
                    processed_count += 1
                else:
                    print(f"✗ Failed to export {transcript_file.name}")
                    
            except Exception as e:
                print(f"✗ Failed to process {transcript_file.name}: {e}")
        
        return processed_count
    
    # Example usage (would need actual files)
    print("Batch processing example:")
    print("processed_count = process_directory('./input_data', './output_highlights')")
    print("This would process all JSON files in input_data/ and save highlights to output_highlights/")


def example_4_streaming_processing():
    """Example 4: Processing large files in chunks."""
    print("\n=== Example 4: Streaming/Chunked Processing ===")
    
    class StreamingProcessor:
        """Process engagement in chunks for long-form content."""
        
        def __init__(self, chunk_duration_sec: int = 300):  # 5-minute chunks
            self.detector = EngagementDetector()
            self.chunk_duration = chunk_duration_sec
        
        def process_streaming(self, transcript_segments, audio_data=None):
            """Process transcript in chunks."""
            all_highlights = []
            
            # Group segments into chunks
            chunks = self._create_chunks(transcript_segments)
            
            for i, chunk_segments in enumerate(chunks):
                print(f"Processing chunk {i+1}/{len(chunks)}")
                
                # Create chunk transcript
                from engagement_app.pipeline.load_data import WhisperTranscript
                chunk_transcript = WhisperTranscript(segments=chunk_segments)
                
                # Process chunk
                highlights = self.detector.process_data(
                    transcript=chunk_transcript,
                    audio_data=audio_data,  # Could slice audio for chunk
                    episode_id=f"chunk_{i}"
                )
                
                all_highlights.extend(highlights)
                
                # Yield results for real-time processing
                yield highlights
            
            return all_highlights
        
        def _create_chunks(self, segments):
            """Split segments into time-based chunks."""
            chunks = []
            current_chunk = []
            chunk_start_time = 0
            
            for segment in segments:
                if segment.start >= chunk_start_time + self.chunk_duration:
                    if current_chunk:
                        chunks.append(current_chunk)
                    current_chunk = [segment]
                    chunk_start_time = segment.start
                else:
                    current_chunk.append(segment)
            
            if current_chunk:
                chunks.append(current_chunk)
            
            return chunks
    
    print("Streaming processor example:")
    print("processor = StreamingProcessor(chunk_duration_sec=300)")
    print("for chunk_highlights in processor.process_streaming(transcript.segments):")
    print("    print(f'Chunk processed: {len(chunk_highlights)} highlights')")


def example_5_web_integration():
    """Example 5: Web API integration."""
    print("\n=== Example 5: Web API Integration ===")
    
    # Example Flask integration
    flask_code = '''
from flask import Flask, request, jsonify
from engagement_app.api import QuickProcessor
import tempfile
import os

app = Flask(__name__)

@app.route('/analyze', methods=['POST'])
def analyze_engagement():
    """API endpoint for engagement analysis."""
    try:
        # Get uploaded files
        transcript_file = request.files.get('transcript')
        audio_file = request.files.get('audio')  # Optional
        
        if not transcript_file:
            return jsonify({'error': 'Transcript file required'}), 400
        
        # Save to temporary files
        with tempfile.TemporaryDirectory() as temp_dir:
            transcript_path = os.path.join(temp_dir, 'transcript.json')
            transcript_file.save(transcript_path)
            
            audio_path = None
            if audio_file:
                audio_path = os.path.join(temp_dir, 'audio.wav')
                audio_file.save(audio_path)
            
            # Process
            highlights = QuickProcessor.extract_highlights(
                transcript_path=transcript_path,
                audio_path=audio_path,
                top_k=request.json.get('top_k', 5)
            )
            
            return jsonify({
                'status': 'success',
                'highlights': highlights,
                'count': len(highlights)
            })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
'''
    
    print("Flask API integration example:")
    print(flask_code)


def example_6_data_pipeline():
    """Example 6: Integration with data pipelines."""
    print("\n=== Example 6: Data Pipeline Integration ===")
    
    # Example pipeline function
    def engagement_pipeline_step(input_data: dict) -> dict:
        """
        Pipeline step that adds engagement analysis to existing data.
        
        Args:
            input_data: Dictionary with 'transcript_path' and optional 'audio_path'
            
        Returns:
            Dictionary with original data plus 'highlights' and 'engagement_summary'
        """
        try:
            # Extract highlights
            highlights = quick_highlights(
                transcript_path=input_data['transcript_path'],
                audio_path=input_data.get('audio_path'),
                top_k=10
            )
            
            # Create summary statistics
            engagement_summary = {
                'total_highlights': len(highlights),
                'avg_engagement_score': sum(h['engagement_score'] for h in highlights) / len(highlights) if highlights else 0,
                'total_highlight_duration': sum(h['duration'] for h in highlights),
                'top_cues': list(set(cue for h in highlights for cue in h['cues']))
            }
            
            # Return enriched data
            return {
                **input_data,
                'highlights': highlights,
                'engagement_summary': engagement_summary,
                'processing_status': 'success'
            }
            
        except Exception as e:
            return {
                **input_data,
                'processing_status': 'failed',
                'error': str(e)
            }
    
    # Example usage in a pipeline
    sample_input = {
        'episode_id': 'ep_001',
        'transcript_path': 'data/ep_001_transcript.json',
        'audio_path': 'data/ep_001_audio.wav',
        'metadata': {'duration': 3600, 'topic': 'technology'}
    }
    
    print("Data pipeline integration example:")
    print("result = engagement_pipeline_step(sample_input)")
    print("This would add engagement analysis to existing data processing pipelines")


def main():
    """Run all examples."""
    print("Vido Tide Integration Examples")
    print("=" * 50)
    
    try:
        example_1_quick_start()
        example_2_custom_config()
        example_3_batch_processing()
        example_4_streaming_processing()
        example_5_web_integration()
        example_6_data_pipeline()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        print("\nNext steps:")
        print("1. Install the package: pip install -e .")
        print("2. Try these examples with your own data")
        print("3. Customize configurations for your use case")
        print("4. Integrate into your existing pipelines")
        
    except Exception as e:
        print(f"Example failed: {e}")
        print("Make sure you have installed the package and have sample data available")


if __name__ == "__main__":
    main()
