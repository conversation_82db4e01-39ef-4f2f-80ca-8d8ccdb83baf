version: '3.8'

services:
  # Production service
  vido-tide:
    build:
      context: .
      target: production
    image: vido-tide:latest
    container_name: vido-tide-app
    volumes:
      - ./data:/home/<USER>/data
      - ./outputs:/home/<USER>/outputs
    environment:
      - PYTHONPATH=/home/<USER>
    command: >
      engagement-pipeline
      --transcript-path /home/<USER>/data/transcript.json
      --audio-path /home/<USER>/data/audio.wav
      --output-dir /home/<USER>/outputs
      --episode-id demo
    networks:
      - vido-tide-network

  # Development service
  vido-tide-dev:
    build:
      context: .
      target: development
    image: vido-tide:dev
    container_name: vido-tide-dev
    volumes:
      - .:/home/<USER>
      - ./data:/home/<USER>/data
      - ./outputs:/home/<USER>/outputs
    environment:
      - PYTHONPATH=/home/<USER>
    ports:
      - "8888:8888"  # For Jupyter if needed
      - "5000:5000"  # For Flask API
    command: bash
    stdin_open: true
    tty: true
    networks:
      - vido-tide-network

  # API service
  vido-tide-api:
    build:
      context: .
      target: production
    image: vido-tide:latest
    container_name: vido-tide-api
    volumes:
      - ./data:/home/<USER>/data
      - ./outputs:/home/<USER>/outputs
    environment:
      - PYTHONPATH=/home/<USER>
      - FLASK_ENV=production
    ports:
      - "5000:5000"
    command: python -c "
      from flask import Flask, request, jsonify;
      from engagement_app.api import QuickProcessor;
      import tempfile, os;
      app = Flask(__name__);
      @app.route('/health', methods=['GET'])
      def health(): return jsonify({'status': 'healthy'});
      @app.route('/analyze', methods=['POST'])
      def analyze():
        try:
          transcript_file = request.files.get('transcript');
          audio_file = request.files.get('audio');
          if not transcript_file: return jsonify({'error': 'Transcript required'}), 400;
          with tempfile.TemporaryDirectory() as temp_dir:
            transcript_path = os.path.join(temp_dir, 'transcript.json');
            transcript_file.save(transcript_path);
            audio_path = None;
            if audio_file:
              audio_path = os.path.join(temp_dir, 'audio.wav');
              audio_file.save(audio_path);
            highlights = QuickProcessor.extract_highlights(transcript_path, audio_path, request.json.get('top_k', 5));
            return jsonify({'status': 'success', 'highlights': highlights, 'count': len(highlights)});
        except Exception as e: return jsonify({'error': str(e)}), 500;
      app.run(host='0.0.0.0', port=5000)
      "
    networks:
      - vido-tide-network

  # Batch processing service
  vido-tide-batch:
    build:
      context: .
      target: production
    image: vido-tide:latest
    container_name: vido-tide-batch
    volumes:
      - ./data:/home/<USER>/data
      - ./outputs:/home/<USER>/outputs
    environment:
      - PYTHONPATH=/home/<USER>
    command: python -c "
      import os;
      from pathlib import Path;
      from engagement_app.api import EngagementDetector;
      detector = EngagementDetector();
      input_dir = Path('/home/<USER>/data');
      output_dir = Path('/home/<USER>/outputs');
      output_dir.mkdir(exist_ok=True);
      for transcript_file in input_dir.glob('*.json'):
        audio_file = None;
        for ext in ['.wav', '.mp3', '.flac']:
          potential_audio = input_dir / f'{transcript_file.stem}{ext}';
          if potential_audio.exists():
            audio_file = potential_audio;
            break;
        try:
          highlights = detector.process_files(transcript_file, audio_file, transcript_file.stem);
          output_file = output_dir / f'{transcript_file.stem}_highlights.json';
          detector.export_highlights(highlights, transcript_file.stem, output_file);
          print(f'✓ Processed {transcript_file.name} -> {len(highlights)} highlights');
        except Exception as e:
          print(f'✗ Failed to process {transcript_file.name}: {e}');
      "
    networks:
      - vido-tide-network

  # Testing service
  vido-tide-test:
    build:
      context: .
      target: development
    image: vido-tide:dev
    container_name: vido-tide-test
    volumes:
      - .:/home/<USER>
    environment:
      - PYTHONPATH=/home/<USER>
    command: python -m pytest engagement_app/tests/ -v --cov=engagement_app
    networks:
      - vido-tide-network

networks:
  vido-tide-network:
    driver: bridge

volumes:
  vido-tide-data:
    driver: local
