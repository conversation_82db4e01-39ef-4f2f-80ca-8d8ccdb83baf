# Vido Tide Integration Guide

This guide shows how to integrate the Vido Tide audience engagement detection system into your projects.

## Installation

### As a pip package (Recommended)

```bash
# Install from source
pip install -e .

# Or install with specific extras
pip install -e ".[llm,audio-extra]"  # Include LLM and audio processing extras
```

### Using Docker

```bash
# Build Docker image
docker build -t vido-tide .

# Run processing
docker run -v /path/to/data:/data vido-tide \
  engagement-pipeline \
  --transcript-path /data/transcript.json \
  --audio-path /data/audio.wav \
  --output-dir /data/output
```

## Quick Start

### 1. Simple Highlight Extraction

```python
from engagement_app.api import quick_highlights

# Extract top 5 highlights from transcript
highlights = quick_highlights(
    transcript_path="path/to/whisper_transcript.json",
    audio_path="path/to/audio.wav",  # Optional
    top_k=5
)

for highlight in highlights:
    print(f"Highlight: {highlight['start_sec']:.1f}s - {highlight['end_sec']:.1f}s")
    print(f"Score: {highlight['engagement_score']:.3f}")
    print(f"Text: {highlight['transcript'][:100]}...")
    print(f"Cues: {', '.join(highlight['cues'])}")
    print("-" * 50)
```

### 2. Process to JSON File

```python
from engagement_app.api import QuickProcessor

# Process and save to JSON
success = QuickProcessor.process_to_json(
    transcript_path="transcript.json",
    output_path="highlights.json",
    audio_path="audio.wav",  # Optional
    top_k=10
)

if success:
    print("Highlights saved to highlights.json")
```

### 3. Advanced Usage with Custom Configuration

```python
from engagement_app.api import EngagementDetector

# Custom configuration
config = {
    'features': {
        'text': {
            'emotion_threshold': 0.2,  # Higher threshold for emotions
            'sentiment_window': 5
        },
        'audio': {
            'laughter_threshold': 0.3,  # Lower threshold for laughter
            'sample_rate': 16000
        }
    },
    'scoring': {
        'weights': {
            'emotion': 0.40,      # Emphasize emotions more
            'laughter': 0.15,     # Emphasize laughter more
            'sentiment_delta': 0.10,
            'rhetorical': 0.10,
            'topic_shift': 0.15,
            'keyword_salience': 0.05,
            'applause': 0.03,
            'prosody': 0.02
        }
    },
    'highlights': {
        'min_duration_sec': 20,   # Shorter minimum duration
        'max_duration_sec': 120,  # Shorter maximum duration
        'top_k': 15
    }
}

# Initialize detector with custom config
detector = EngagementDetector(config)

# Process files
highlights = detector.process_files(
    transcript_path="transcript.json",
    audio_path="audio.wav",
    episode_id="my_episode_001"
)

# Export to JSON
detector.export_highlights(
    highlights=highlights,
    episode_id="my_episode_001",
    output_path="custom_highlights.json"
)
```

## Integration Patterns

### 1. Batch Processing Pipeline

```python
import os
from pathlib import Path
from engagement_app.api import EngagementDetector

def process_directory(input_dir: str, output_dir: str):
    """Process all transcript files in a directory."""
    detector = EngagementDetector()
    
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    for transcript_file in input_path.glob("*.json"):
        # Look for corresponding audio file
        audio_file = None
        for ext in ['.wav', '.mp3', '.flac']:
            potential_audio = input_path / f"{transcript_file.stem}{ext}"
            if potential_audio.exists():
                audio_file = potential_audio
                break
        
        # Process
        try:
            highlights = detector.process_files(
                transcript_path=transcript_file,
                audio_path=audio_file,
                episode_id=transcript_file.stem
            )
            
            # Export
            output_file = output_path / f"{transcript_file.stem}_highlights.json"
            detector.export_highlights(
                highlights=highlights,
                episode_id=transcript_file.stem,
                output_path=output_file
            )
            
            print(f"✓ Processed {transcript_file.name} -> {len(highlights)} highlights")
            
        except Exception as e:
            print(f"✗ Failed to process {transcript_file.name}: {e}")

# Usage
process_directory("./input_transcripts", "./output_highlights")
```

### 2. Web API Integration

```python
from flask import Flask, request, jsonify
from engagement_app.api import QuickProcessor
import tempfile
import os

app = Flask(__name__)

@app.route('/analyze', methods=['POST'])
def analyze_engagement():
    """API endpoint for engagement analysis."""
    try:
        # Get uploaded files
        transcript_file = request.files.get('transcript')
        audio_file = request.files.get('audio')  # Optional
        
        if not transcript_file:
            return jsonify({'error': 'Transcript file required'}), 400
        
        # Save to temporary files
        with tempfile.TemporaryDirectory() as temp_dir:
            transcript_path = os.path.join(temp_dir, 'transcript.json')
            transcript_file.save(transcript_path)
            
            audio_path = None
            if audio_file:
                audio_path = os.path.join(temp_dir, 'audio.wav')
                audio_file.save(audio_path)
            
            # Process
            highlights = QuickProcessor.extract_highlights(
                transcript_path=transcript_path,
                audio_path=audio_path,
                top_k=request.json.get('top_k', 5)
            )
            
            return jsonify({
                'status': 'success',
                'highlights': highlights,
                'count': len(highlights)
            })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
```

### 3. Streaming/Real-time Processing

```python
from engagement_app.api import EngagementDetector
from engagement_app.pipeline.load_data import WhisperTranscript, WhisperSegment
import time

class StreamingProcessor:
    """Process engagement in chunks for long-form content."""
    
    def __init__(self, chunk_duration_sec: int = 300):  # 5-minute chunks
        self.detector = EngagementDetector()
        self.chunk_duration = chunk_duration_sec
    
    def process_streaming(self, transcript_segments, audio_data=None):
        """Process transcript in chunks."""
        all_highlights = []
        
        # Group segments into chunks
        chunks = self._create_chunks(transcript_segments)
        
        for i, chunk_segments in enumerate(chunks):
            print(f"Processing chunk {i+1}/{len(chunks)}")
            
            # Create chunk transcript
            chunk_transcript = WhisperTranscript(segments=chunk_segments)
            
            # Process chunk
            highlights = self.detector.process_data(
                transcript=chunk_transcript,
                audio_data=audio_data,  # Could slice audio for chunk
                episode_id=f"chunk_{i}"
            )
            
            all_highlights.extend(highlights)
            
            # Optional: yield results for real-time processing
            yield highlights
        
        return all_highlights
    
    def _create_chunks(self, segments):
        """Split segments into time-based chunks."""
        chunks = []
        current_chunk = []
        chunk_start_time = 0
        
        for segment in segments:
            if segment.start >= chunk_start_time + self.chunk_duration:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = [segment]
                chunk_start_time = segment.start
            else:
                current_chunk.append(segment)
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
```

## Data Models

### Input Data Models

```python
from engagement_app.pipeline.load_data import WhisperTranscript, WhisperSegment, AudioData

# Whisper transcript format
transcript = WhisperTranscript(
    segments=[
        WhisperSegment(
            start=0.0,
            end=5.2,
            text="Hello, welcome to our podcast!"
        ),
        # ... more segments
    ],
    language="en",
    duration=3600.0
)

# Audio data format
audio_data = AudioData(
    audio=numpy_array,      # numpy array of audio samples
    sample_rate=16000,      # sample rate in Hz
    duration=3600.0,        # duration in seconds
    channels=1,             # number of channels
    file_path="audio.wav"   # original file path
)
```

### Output Data Models

```python
from engagement_app.pipeline.postprocess import HighlightSegment

# Highlight segment
highlight = HighlightSegment(
    start_sec=120.5,
    end_sec=185.2,
    sentences=[
        {
            'text': 'This is amazing!',
            'engagement_score': 0.85,
            'start_sec': 120.5,
            'end_sec': 125.0
        }
        # ... more sentences
    ]
)

# Access properties
print(f"Duration: {highlight.duration}")
print(f"Max score: {highlight.max_engagement_score}")
print(f"Transcript: {highlight.transcript}")
print(f"Cues: {highlight.cues}")
```

## Configuration Options

### Feature Weights

Customize how different features contribute to engagement scoring:

```python
config = {
    'scoring': {
        'weights': {
            'emotion': 0.30,        # Emotional content (joy, excitement, etc.)
            'sentiment_delta': 0.15, # Sentiment changes
            'rhetorical': 0.10,     # Questions, exclamations
            'topic_shift': 0.20,    # Topic transitions
            'keyword_salience': 0.05, # Important keywords
            'laughter': 0.07,       # Laughter detection
            'applause': 0.05,       # Applause detection
            'prosody': 0.05,        # Prosodic emphasis
            'clap_similarity': 0.03  # Audio-text alignment
        }
    }
}
```

### Highlight Generation

Control how highlights are selected and formatted:

```python
config = {
    'highlights': {
        'selection_threshold_std': 1.0,  # Standard deviations above mean
        'min_duration_sec': 30,          # Minimum highlight duration
        'max_duration_sec': 180,         # Maximum highlight duration
        'merge_gap_sec': 8,              # Merge highlights within 8 seconds
        'boundary_extension_sentences': 1, # Extend boundaries by 1 sentence
        'top_k': 10,                     # Return top 10 highlights
        'ranking_duration_weight': 0.3   # Weight duration in ranking
    }
}
```

## Performance Considerations

### Memory Usage

- **Large files**: Use streaming processing for files > 1 hour
- **Batch processing**: Process multiple files sequentially to avoid memory issues
- **Audio features**: Disable audio processing if not needed to save memory

### Processing Speed

- **Target**: ~7.5 minutes for 1 hour of audio on NVIDIA T4
- **CPU-only**: Expect 2-3x slower processing
- **Optimization**: Disable LLM reranking for faster processing

### GPU Usage

```python
# Check GPU availability
import torch
if torch.cuda.is_available():
    print(f"GPU available: {torch.cuda.get_device_name()}")
else:
    print("Using CPU processing")

# Configure for GPU usage
config = {
    'processing': {
        'use_gpu': True,
        'batch_size': 16  # Adjust based on GPU memory
    }
}
```

## Error Handling

```python
from engagement_app.api import EngagementDetector
import logging

# Enable detailed logging
logging.basicConfig(level=logging.INFO)

try:
    detector = EngagementDetector()
    highlights = detector.process_files("transcript.json", "audio.wav")
    
except FileNotFoundError as e:
    print(f"File not found: {e}")
except ValueError as e:
    print(f"Invalid input: {e}")
except RuntimeError as e:
    print(f"Processing error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Next Steps

1. **Install the package**: `pip install -e .`
2. **Try the quick start examples** with your data
3. **Customize configuration** for your use case
4. **Integrate into your pipeline** using the patterns above
5. **Monitor performance** and adjust settings as needed

For more examples and advanced usage, see the `examples/` directory and test files in `engagement_app/tests/`.
