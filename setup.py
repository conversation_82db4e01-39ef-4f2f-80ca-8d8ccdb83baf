#!/usr/bin/env python3
from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

setup(
    name="engagement_app",
    version="1.0.0",
    author="MLOps Engineering Team",
    author_email="<EMAIL>",
    description="A production-ready system for detecting audience engagement from long-form audio content using unsupervised methods",
    long_description=long_description,
    long_description_content_type="text/markdown",
    license="MIT",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Multimedia :: Sound/Audio :: Analysis",
        "Topic :: Text Processing :: Linguistic",
    ],
    python_requires=">=3.10",
    install_requires=[
        # Core ML/NLP Dependencies
        "spacy>=3.6.0",
        "transformers>=4.30.0",
        "torch>=2.0.0",
        "accelerate>=0.20.0",
        "librosa>=0.10.0",
        "bertopic>=0.15.0",
        "panns-inference>=0.1.0",
        "nltk>=3.8.0",
        "changeforest>=1.0.0",
        "keybert>=0.7.0",
        "soundfile>=0.12.0",
        
        # Data Processing
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "scikit-learn>=1.3.0",
        "scipy>=1.10.0",
        
        # Audio Processing
        "torchaudio>=2.0.0",
        "audioread>=3.0.0",
        
        # API and HTTP
        "requests>=2.31.0",
        
        # Configuration and Validation
        "jsonschema>=4.17.0",
        "pyyaml>=6.0",
        "python-dotenv>=1.0.0",
        
        # Utilities
        "tqdm>=4.65.0",
        "python-dateutil>=2.8.0",
        
        # Performance
        "joblib>=1.3.0",
        "numba>=0.57.0",
        
        # Logging
        "structlog>=23.1.0",
        "openai>=1.0.0",
        "google-generativeai>=0.3.0",
    ],
    extras_require={
        "llm": [
            "openai>=1.0.0",
            "google-generativeai>=0.3.0",
        ],
        "dev": [
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
            "pre-commit>=3.0.0",
        ],
        "test": [
            "pytest>=7.0.0",
            "pytest-cov>=4.1.0",
            "pytest-mock>=3.11.0",
            "pytest-xdist>=3.0.0",
        ],
    },
    include_package_data=True,
    package_data={
        "engagement_app": [
            "config/*.yaml",
            "schemas/*.json",
        ],
    },
    entry_points={
        "console_scripts": [
            "vido-tide=engagement_app.api:main",
        ],
    },
)
