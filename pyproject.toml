[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "engagement_app"
version = "1.0.0"
description = "A production-ready system for detecting audience engagement from long-form audio content using unsupervised methods"
readme = "engagement_app/README.md"
license = {text = "MIT"}
authors = [
    {name = "MLOps Engineering Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "MLOps Engineering Team", email = "<EMAIL>"}
]
keywords = [
    "machine-learning",
    "audio-processing",
    "nlp",
    "engagement-detection",
    "transformers",
    "pytorch",
    "mlops"
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Multimedia :: Sound/Audio :: Analysis",
    "Topic :: Text Processing :: Linguistic",
]
requires-python = ">=3.10"
dependencies = [
    # Core ML/NLP Dependencies
    "spacy>=3.6.0",
    "transformers>=4.30.0",
    "torch>=2.0.0",
    "accelerate>=0.20.0",
    "librosa>=0.10.0",
    "bertopic>=0.15.0",
    "panns-inference>=0.1.0",
    "nltk>=3.8.0",
    "changeforest>=1.0.0",
    "keybert>=0.7.0",
    "soundfile>=0.12.0",
    
    # Data Processing
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "scipy>=1.10.0",
    
    # Audio Processing
    "torchaudio>=2.0.0",
    "audioread>=3.0.0",
    
    # API and HTTP
    "requests>=2.31.0",
    
    # Configuration and Validation
    "jsonschema>=4.17.0",
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0",
    
    # Utilities
    "tqdm>=4.65.0",
    "python-dateutil>=2.8.0",
    
    # Performance
    "joblib>=1.3.0",
    "numba>=0.57.0",
    
    # Logging
    "structlog>=23.1.0",
]

[project.optional-dependencies]
llm = [
    "openai>=1.0.0",
    "google-generativeai>=0.3.0",
]
dev = [
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "pytest-xdist>=3.0.0",
]
audio-extra = [
    "ffmpeg-python>=0.2.0",
]
all = [
    "audience-engagement-detection[llm,dev,test,audio-extra]"
]

[project.urls]
Homepage = "https://github.com/your-org/audience-engagement-detection"
Documentation = "https://github.com/your-org/audience-engagement-detection#readme"
Repository = "https://github.com/your-org/audience-engagement-detection.git"
Issues = "https://github.com/your-org/audience-engagement-detection/issues"
Changelog = "https://github.com/your-org/audience-engagement-detection/releases"

[project.scripts]
engagement-pipeline = "engagement_app.scripts.run_pipeline:main"
engagement-benchmark = "engagement_app.scripts.benchmark:main"
engagement-validate = "engagement_app.scripts.validate_installation:main"

[tool.setuptools]
zip-safe = false
include-package-data = true

[tool.setuptools.packages.find]
where = ["."]
include = ["engagement_app*"]
exclude = ["tests*", "*.tests*", "*.tests"]

[tool.setuptools.package-data]
"engagement_app" = [
    "config/*.yaml",
    "schemas/*.json",
    "data/**/*",
]

# Black code formatting
[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# MyPy type checking
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "librosa.*",
    "panns_inference.*",
    "bertopic.*",
    "changeforest.*",
    "keybert.*",
    "soundfile.*",
    "audioread.*",
    "numba.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=engagement_app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["engagement_app/tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gpu: marks tests that require GPU",
]

# Coverage configuration
[tool.coverage.run]
source = ["engagement_app"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/scripts/setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

# Flake8 configuration (in setup.cfg format for compatibility)
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg-info",
]
per-file-ignores = [
    "__init__.py:F401",
    "tests/*:S101",
]
