#!/usr/bin/env python3

import os
import sys
from pathlib import Path

# Add the project root directory to Python path
project_root = str(Path(__file__).parent.parent.absolute())
if project_root not in sys.path:
    sys.path.insert(0, project_root)

print("Python path:", sys.path)

try:
    import engagement_app
    from engagement_app.api import EngagementDetector, QuickProcessor, quick_highlights
    print("Successfully imported engagement_app")
except ImportError as e:
    print(f"Import error: {e}")
